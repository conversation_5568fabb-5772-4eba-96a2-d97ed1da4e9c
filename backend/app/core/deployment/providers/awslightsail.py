import os
from pathlib import Path
from jinja2 import Environment, FileSystemLoader
from app.core.deployment.providers.base import ProviderStrategy
from app.models.deployment import Deployment as DeploymentModel
from app.core.config import get_settings

class AwsLightsailStrategy(ProviderStrategy):
    """Concrete strategy for AwsLightsail deployments."""

    def generate_terraform_files(self, deployment_dir: Path, deployment: DeploymentModel) -> None:
        """Generate all necessary Terraform files for a AwsLightsail deployment."""
        template_dir = Path(__file__).parent.parent / "terraform_templates"
        env = Environment(loader=FileSystemLoader(template_dir))

        self._generate_awslightsail_main_files(deployment_dir, env, deployment)
        self._generate_tfvars(deployment_dir, env, deployment)
        self._generate_config_template(deployment_dir, env, deployment)

    def _generate_awslightsail_main_files(self, deployment_dir: Path, env: Environment, deployment: DeploymentModel) -> None:
        """Generate main AwsLightsail Terraform files from templates."""
        main_template = env.get_template("awslightsail_main.tf.j2")
        variables_template = env.get_template("awslightsail_variables.tf.j2")
        outputs_template = env.get_template("awslightsail_outputs.tf.j2")
        startup_script_template = env.get_template("awslightsail_startup-script.sh")

        with open(deployment_dir / "main.tf", "w") as f:
            f.write(main_template.render(package=deployment.package))
        with open(deployment_dir / "variables.tf", "w") as f:
            f.write(variables_template.render(package=deployment.package))
        with open(deployment_dir / "outputs.tf", "w") as f:
            f.write(outputs_template.render(package=deployment.package))
        with open(deployment_dir / "startup-script.sh", "w") as f:
            f.write(startup_script_template.render())

    def _generate_tfvars(self, deployment_dir: Path, env: Environment, deployment: DeploymentModel) -> None:
        """Generate the terraform.tfvars file for AwsLightsail."""
        template = env.get_template("awslightsail_terraform.tfvars.j2")
        tfvars_content = template.render(
            **self._get_terraform_vars(deployment)
        )
        with open(deployment_dir / "terraform.tfvars", "w") as f:
            f.write(tfvars_content)

    def _get_terraform_vars(self, deployment: DeploymentModel) -> dict:
        """Helper to construct a dictionary of Terraform variables."""

        ssh_public_key = ""
        ssh_key_file_path = os.environ.get("AWSLIGHTSAIL_SSH_FILE")

        if ssh_key_file_path:
            try:
                with open(ssh_key_file_path, 'r') as f:
                    ssh_public_key = f.read().strip()
                print(f"Successfully read SSH key from {ssh_key_file_path}")
            except Exception as e:
                print(f"Error reading SSH key file: {e}")

        if not ssh_public_key:
            ssh_public_key = get_settings().SSH_PUBLIC_KEY or ""

        client_name_lower = deployment.client_name.lower().replace(' ', '-')
        vars_dict = {
            "aws_region": deployment.region,
            "aws_access_key": get_settings().AWS_ACCESS_KEY_ID,
            "aws_secret_key": get_settings().AWS_SECRET_ACCESS_KEY,
            "aws_availability_zone": f"{deployment.region}a",
            "aws_bundle_id": deployment.instance_type,
            "aws_blueprint_id": "ubuntu_22_04",  # Hardcoded for now
            "ssh_public_key": ssh_public_key,
            "instance_name": f"pangolin-{client_name_lower}",
            "client_name": deployment.client_name,
            "client_name_lower": client_name_lower,
            "client_id": deployment.client_id,
            "package": deployment.package,
            "komodo_provider_endpoint": get_settings().KOMODO_PROVIDER_ENDPOINT,
            "komodo_api_key": get_settings().KOMODO_API_KEY,
            "komodo_api_secret": get_settings().KOMODO_API_SECRET,
            "github_token": get_settings().GITHUB_TOKEN,
            "domain": deployment.domain,
            "admin_email": deployment.admin_email,
            "admin_username": deployment.admin_username,
            "admin_password": deployment.admin_password,
            "admin_subdomain": deployment.admin_subdomain,
            "postgres_user": deployment.postgres_user,
            "postgres_password": deployment.postgres_password,
            "postgres_host": deployment.postgres_host,
            "github_repo": deployment.github_repo,
            "premium_package": (deployment.package in ["Pangolin+", "Pangolin+AI"]),
        }
        if deployment.package in ["Pangolin+", "Pangolin+AI"]:
            vars_dict.update({
                "crowdsec_enrollment_key": deployment.crowdsec_enrollment_key,
                "static_page_subdomain": deployment.static_page_subdomain,
                "maxmind_license_key": deployment.maxmind_license_key,
            })
        if deployment.package == "Pangolin+AI":
            vars_dict.update({
                "oauth_client_id": deployment.oauth_client_id,
                "oauth_client_secret": deployment.oauth_client_secret,
                "openai_api_key": deployment.openai_api_key,
                "komodo_host_ip": deployment.komodo_host_ip,
                "komodo_passkey": deployment.komodo_passkey,
            })
        return vars_dict

    def _generate_config_template(self, deployment_dir: Path, env: Environment, deployment: DeploymentModel) -> None:
        """Generate the config-template.toml file."""
        template = env.get_template("awslightsail_config-template.toml.j2")
        content = template.render(
            client_name=deployment.client_name,
            client_name_lower=deployment.client_name.lower().replace(' ', '-'),
            domain=deployment.domain,
            admin_email=deployment.admin_email,
            admin_username=deployment.admin_username,
            admin_password=deployment.admin_password,
            admin_subdomain=deployment.admin_subdomain,
            postgres_user=deployment.postgres_user,
            postgres_password=deployment.postgres_password,
            postgres_host=deployment.postgres_host,
            github_repo=deployment.github_repo,
            package=deployment.package,
            crowdsec_enrollment_key=deployment.crowdsec_enrollment_key,
            static_page_subdomain=deployment.static_page_subdomain,
            maxmind_license_key=deployment.maxmind_license_key,
            oauth_client_id=deployment.oauth_client_id,
            oauth_client_secret=deployment.oauth_client_secret,
            openai_api_key=deployment.openai_api_key,
            komodo_host_ip=deployment.komodo_host_ip,
            komodo_passkey=deployment.komodo_passkey,
        )
        with open(deployment_dir / "config-template.toml", "w") as f:
            f.write(content)