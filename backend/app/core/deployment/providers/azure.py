import os
from pathlib import Path
from jinja2 import Environment, FileSystemLoader
from app.core.deployment.providers.base import ProviderStrategy
from app.models.deployment import Deployment as DeploymentModel
from app.core.config import get_settings

class AzureStrategy(ProviderStrategy):
    """Concrete strategy for Google Cloud deployments."""

    def generate_terraform_files(self, deployment_dir: Path, deployment: DeploymentModel) -> None:
        """Generate all necessary Terraform files for a Google Cloud deployment."""
        template_dir = Path(__file__).parent.parent / "terraform_templates"
        env = Environment(loader=FileSystemLoader(template_dir))

        # Generate main.tf, variables.tf, outputs.tf, and startup-script.sh
        self._generate_azure_main_files(deployment_dir, env, deployment)

        # Generate terraform.tfvars
        self._generate_tfvars(deployment_dir, env, deployment)

        # Generate config-template.toml
        self._generate_config_template(deployment_dir, env, deployment)

    def _generate_azure_main_files(self, deployment_dir: Path, env: Environment, deployment: DeploymentModel) -> None:
        """Generate main GCP Terraform files from templates."""
        main_template = env.get_template("azure_main.tf.j2")
        variables_template = env.get_template("azure_variables.tf.j2")
        outputs_template = env.get_template("azure_outputs.tf.j2")
        startup_script_template = env.get_template("azure_startup-script.sh")

        with open(deployment_dir / "main.tf", "w") as f:
            f.write(main_template.render(package=deployment.package))
        with open(deployment_dir / "variables.tf", "w") as f:
            f.write(variables_template.render(package=deployment.package))
        with open(deployment_dir / "outputs.tf", "w") as f:
            f.write(outputs_template.render(package=deployment.package))
        with open(deployment_dir / "startup-script.sh", "w") as f:
            f.write(startup_script_template.render())

    def _generate_tfvars(self, deployment_dir: Path, env: Environment, deployment: DeploymentModel) -> None:
        """Generate the terraform.tfvars file for GCP."""
        template = env.get_template("azure_terraform.tfvars.j2")
        tfvars_content = template.render(
            **self._get_terraform_vars(deployment)
        )
        with open(deployment_dir / "terraform.tfvars", "w") as f:
            f.write(tfvars_content)

    def _get_terraform_vars(self, deployment: DeploymentModel) -> dict:
        """Helper to construct a dictionary of Terraform variables."""
        client_name_lower = deployment.client_name.lower().replace(' ', '-')

        ssh_public_key = ""
        ssh_key_file_path = os.environ.get("AZURE_SSH_FILE")
        
        if ssh_key_file_path:
            try:
                # Make sure to handle both absolute and relative paths
                if not ssh_key_file_path.startswith('/'):
                    ssh_key_file_path = f"/{ssh_key_file_path}"  # Ensure absolute path
                
                with open(ssh_key_file_path, 'r') as f:
                    ssh_public_key = f.read().strip()
                print(f"Successfully read SSH key from {ssh_key_file_path}")
            except Exception as e:
                print(f"Error reading SSH key file: {e}")
                # Fallback is handled below
        
        if not ssh_public_key:
            ssh_public_key = get_settings().SSH_PUBLIC_KEY or ""

        vars_dict = {
            "azure_subscription_id": get_settings().AZURE_SUBSCRIPTION_ID,
            "azure_client_id": get_settings().AZURE_CLIENT_ID,
            "azure_client_secret": get_settings().AZURE_CLIENT_SECRET,
            "azure_tenant_id": get_settings().AZURE_TENANT_ID,
            "azure_location": deployment.region,
            "instance_name": f"pangolin-{client_name_lower}",
            "vm_size": deployment.instance_type,
            "ssh_username": "ubuntu",
            "ssh_public_key": ssh_public_key,
            "instance_tags": ["pangolin", client_name_lower],
            "firewall_name": f"allow-pangolin-{client_name_lower}",
            "client_name": deployment.client_name,
            "client_id": deployment.client_id,
            "package": deployment.package,
            "komodo_provider_endpoint": get_settings().KOMODO_PROVIDER_ENDPOINT,
            "komodo_api_key": get_settings().KOMODO_API_KEY,
            "komodo_api_secret": get_settings().KOMODO_API_SECRET,
            "github_token": get_settings().GITHUB_TOKEN,
            "domain": deployment.domain,
            "admin_email": deployment.admin_email,
            "admin_username": deployment.admin_username,
            "admin_password": deployment.admin_password,
            "admin_subdomain": deployment.admin_subdomain,
            "postgres_user": deployment.postgres_user,
            "postgres_password": deployment.postgres_password,
            "postgres_host": deployment.postgres_host,
            "github_repo": deployment.github_repo,
            "premium_package": (deployment.package == "Premium"),
        }
        if deployment.package == "Premium":
            vars_dict.update({
                "crowdsec_enrollment_key": deployment.crowdsec_enrollment_key,
                "static_page_domain": deployment.static_page_domain,
                "oauth_client_id": deployment.oauth_client_id,
                "oauth_client_secret": deployment.oauth_client_secret,
                "openai_api_key": deployment.openai_api_key,
                "komodo_host_ip": deployment.komodo_host_ip,
            })
        return vars_dict

    def _generate_config_template(self, deployment_dir: Path, env: Environment, deployment: DeploymentModel) -> None:
        """Generate the config-template.toml file."""
        template = env.get_template("azure_config-template.toml.j2")
        content = template.render(
            client_name=deployment.client_name,
            client_name_lower=deployment.client_name.lower().replace(' ', '-'),
            domain=deployment.domain,
            admin_email=deployment.admin_email,
            admin_username=deployment.admin_username,
            admin_password=deployment.admin_password,
            admin_subdomain=deployment.admin_subdomain,
            postgres_user=deployment.postgres_user,
            postgres_password=deployment.postgres_password,
            postgres_host=deployment.postgres_host,
            github_repo=deployment.github_repo,
            package=deployment.package,
            crowdsec_enrollment_key=deployment.crowdsec_enrollment_key,
            static_page_domain=deployment.static_page_domain,
            oauth_client_id=deployment.oauth_client_id,
            oauth_client_secret=deployment.oauth_client_secret,
            openai_api_key=deployment.openai_api_key,
            komodo_host_ip=deployment.komodo_host_ip,
        )
        with open(deployment_dir / "config-template.toml", "w") as f:
            f.write(content)
