
import os
import shutil
import logging
import subprocess
from pathlib import Path
from python_terraform import Terraform # type: ignore

# Set up logger
logger = logging.getLogger(__name__)

class TerraformRunner:
    """A class to encapsulate Terraform command execution."""

    def __init__(self, deployment_dir: Path):
        self.deployment_dir = deployment_dir
        self.terraform = Terraform(working_dir=str(deployment_dir))
        logger.info(f"TerraformRunner initialized with deployment_dir: {deployment_dir}")

    def init_and_apply(self, dry_run: bool = False):
        """Initialize and apply the Terraform configuration."""
        logger.info(f"Starting init_and_apply, dry_run={dry_run}")
        print(f"[TERRAFORM] Starting init_and_apply, dry_run={dry_run}")

        logger.info("Preparing for init...")
        print("[TERRAFORM] Preparing for init...")
        self._prepare_for_init()

        logger.info("Running terraform init...")
        print("[TERRAFORM] Running terraform init...")
        init_result = self.terraform.init()
        logger.info(f"Terraform init result: return_code={init_result[0]}")
        print(f"[TERRAFORM] Terraform init result: return_code={init_result[0]}")
        if init_result[1]:
            logger.info(f"Terraform init stdout: {init_result[1]}")
            print(f"[TERRAFORM] Terraform init stdout: {init_result[1]}")
        if init_result[2]:
            logger.warning(f"Terraform init stderr: {init_result[2]}")
            print(f"[TERRAFORM] Terraform init stderr: {init_result[2]}")

        if init_result[0] != 0:
            logger.error("Terraform init failed, restoring backup")
            print("[TERRAFORM] ERROR: Terraform init failed, restoring backup")
            self._restore_from_init()
            raise Exception(f"Terraform init failed: {init_result[2]}")

        logger.info("Restoring from init...")
        print("[TERRAFORM] Restoring from init...")
        self._restore_from_init()

        if dry_run:
            logger.info("DRY RUN: Mocking terraform apply...")
            print("[TERRAFORM] DRY RUN: Mocking terraform apply...")
            return

        logger.info("Running terraform apply...")
        print("[TERRAFORM] Running terraform apply...")
        apply_result = self.terraform.apply(skip_plan=True)
        logger.info(f"Terraform apply result: return_code={apply_result[0]}")
        print(f"[TERRAFORM] Terraform apply result: return_code={apply_result[0]}")
        if apply_result[1]:
            logger.info(f"Terraform apply stdout: {apply_result[1]}")
            print(f"[TERRAFORM] Terraform apply stdout: {apply_result[1]}")
        if apply_result[2]:
            logger.warning(f"Terraform apply stderr: {apply_result[2]}")
            print(f"[TERRAFORM] Terraform apply stderr: {apply_result[2]}")

        if apply_result[0] != 0:
            logger.error("Terraform apply failed")
            print("[TERRAFORM] ERROR: Terraform apply failed")
            raise Exception(f"Terraform apply failed: {apply_result[2]}")

        logger.info("Terraform init_and_apply completed successfully")
        print("[TERRAFORM] Terraform init_and_apply completed successfully")

        if not dry_run:
            outputs = self.terraform.output()
            logger.info(f"Terraform outputs: {outputs}")
            print(f"[TERRAFORM] Terraform outputs: {outputs}")
            return outputs
        return None

    def destroy(self):
        """Destroy the Terraform-managed infrastructure.
        Includes a fallback path that comments out komodo-provider blocks and re-inits before destroy,
        to ensure compute instances are torn down even if Komodo API is unavailable.
        """
        logger.info("Starting terraform destroy...")
        print("[TERRAFORM] Starting terraform destroy...")

        # Ensure optional vars have defaults to prevent prompts
        self._ensure_optional_vars_have_defaults()

        # First attempt a plain destroy
        command = "terraform destroy -input=false -auto-approve"
        print(f"[TERRAFORM] Running command: cd {self.deployment_dir} && {command}")
        result = os.system(f"cd {self.deployment_dir} && {command}")
        print(f"[TERRAFORM] Terraform destroy result: exit_code={result}")

        if result != 0:
            logger.warning("Terraform destroy failed, retrying with komodo blocks commented and state pruned")
            print("[TERRAFORM] WARN: Destroy failed, retrying with komodo blocks commented and state pruned")
            try:
                # Comment out Komodo blocks and re-init
                self._prepare_for_init()
                init_result = self.terraform.init()
                print(f"[TERRAFORM] Re-init after commenting komodo blocks: rc={init_result[0]}")
                # Prune komodo-related resources from state if they exist
                self._remove_komodo_from_state()
                # Retry destroy
                result = os.system(f"cd {self.deployment_dir} && {command}")
                print(f"[TERRAFORM] Terraform destroy result (retry): exit_code={result}")
            finally:
                # Always restore main.tf to original
                try:
                    self._restore_from_init()
                except Exception:
                    pass

        if result != 0:
            logger.error("Terraform destroy failed")
            print("[TERRAFORM] ERROR: Terraform destroy failed")
            raise Exception(f"Terraform destroy failed with exit code {result}")

        logger.info("Terraform destroy completed successfully")
        print("[TERRAFORM] Terraform destroy completed successfully")

    def _ensure_optional_vars_have_defaults(self):
        """Create an auto tfvars file with empty defaults for optional vars if they're not already set.
        This helps avoid interactive prompts during destroy for Pangolin/BYOVPS deployments that don't use Premium vars.
        """
        tfvars_path = self.deployment_dir / "terraform.tfvars"
        existing_keys = set()
        if tfvars_path.exists():
            try:
                content = tfvars_path.read_text()
                for line in content.splitlines():
                    if "=" in line and not line.strip().startswith("#"):
                        key = line.split("=")[0].strip()
                        if key:
                            existing_keys.add(key)
            except Exception:
                pass

        optional_keys = [
            "crowdsec_enrollment_key",
            "static_page_domain",
            "oauth_client_id",
            "oauth_client_secret",
            "komodo_host_ip",
            "openai_api_key",
        ]

        lines = []
        for key in optional_keys:
            if key not in existing_keys:
                lines.append(f"{key} = \"\"")
        if lines:
            auto_path = self.deployment_dir / "auto_defaults.auto.tfvars"
            auto_path.write_text("\n".join(lines) + "\n")
            print(f"[TERRAFORM] Wrote defaults to {auto_path} for missing optional vars: {', '.join(optional_keys)}")

    def _remove_komodo_from_state(self):
        """Best-effort removal of komodo resources from state to allow destroy to proceed.
        This avoids failures when the Komodo provider endpoints are unreachable.
        """
        try:
            # List state resources
            list_cmd = f"cd {self.deployment_dir} && terraform state list"
            stream = os.popen(list_cmd)
            resources = stream.read().splitlines()
            # Remove any resources with 'komodo-provider' in their address
            for addr in resources:
                if 'komodo-provider' in addr or 'komodo_provider' in addr:
                    rm_cmd = f"cd {self.deployment_dir} && terraform state rm {addr}"
                    print(f"[TERRAFORM] Removing from state: {addr}")
                    os.system(rm_cmd)
        except Exception as e:
            print(f"[TERRAFORM] WARN: Failed to prune komodo resources from state: {e}")

    def _prepare_for_init(self):
        """Comment out the komodo-provider sections before init."""
        logger.info("Preparing main.tf for init by commenting out komodo-provider sections")
        main_tf_path = self.deployment_dir / "main.tf"
        backup_path = self.deployment_dir / "main.tf.bak"
        shutil.copy(main_tf_path, backup_path)

        with open(main_tf_path, 'r') as f:
            content = f.read()

        # Process the content line by line to properly handle nested brackets
        lines = content.split('\n')
        modified_lines = []

        # State tracking variables
        in_komodo_provider_block = False
        in_provider_block = False
        in_resource_block = False
        brace_count = 0

        for line in lines:
            stripped_line = line.strip()
            # Check for komodo-provider in required_providers block
            if not in_komodo_provider_block and not in_provider_block and not in_resource_block and 'komodo-provider = {' in stripped_line:
                in_komodo_provider_block = True
                brace_count = 1  # Opening brace
                modified_lines.append('# ' + line)
                continue

            # Check for provider "komodo-provider" block
            elif not in_komodo_provider_block and not in_provider_block and not in_resource_block and 'provider "komodo-provider"' in stripped_line:
                in_provider_block = True
                brace_count = stripped_line.count('{')
                modified_lines.append('# ' + line)
                continue

            # Check for resource "komodo-provider_user" block
            elif not in_komodo_provider_block and not in_provider_block and not in_resource_block and 'resource "komodo-provider_user"' in stripped_line:
                in_resource_block = True
                brace_count = stripped_line.count('{')
                modified_lines.append('# ' + line)
                continue

            # Inside a block that needs to be commented
            elif in_komodo_provider_block or in_provider_block or in_resource_block:
                # Update brace count
                brace_count += line.count('{') - line.count('}')
                modified_lines.append('# ' + line)

                # Check if block is closed
                if brace_count == 0:
                    if in_komodo_provider_block:
                        in_komodo_provider_block = False
                    elif in_provider_block:
                        in_provider_block = False
                    elif in_resource_block:
                        in_resource_block = False
                continue

            # Line not in a block that needs commenting
            else:
                modified_lines.append(line)

        # Write the modified content
        with open(main_tf_path, "w") as f:
            f.write('\n'.join(modified_lines))

    def _restore_from_init(self):
        """Restore the original main.tf file after init."""
        main_tf_path = self.deployment_dir / "main.tf"
        backup_path = self.deployment_dir / "main.tf.bak"
        shutil.copy(backup_path, main_tf_path)
        os.remove(backup_path)
