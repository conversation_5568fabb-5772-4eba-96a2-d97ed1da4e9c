import csv
import os
from pathlib import Path
from typing import Dict, Optional

from app.db.session import SessionLocal
from app.models.instance_pricing import InstancePricing

class PricingService:
    def __init__(self):
        self.pricing_data = []
        self.load_pricing_data()

    def load_pricing_data(self):
        """Load pricing data from database, fallback to CSV if needed"""
        db = SessionLocal()
        try:
            # Try to load from database first
            db_pricing = db.query(InstancePricing).all()
            if db_pricing:
                # Convert DB objects to dictionaries for consistent access
                self.pricing_data = [{
                    'cloud_provider': item.cloud_provider,
                    'instance_type': item.instance_type,
                    'region': item.region,
                    'hourly_cost': str(item.cost_per_hour),
                    'package_multiplier_pangolin': str(item.package_multiplier_pangolin),
                    'package_multiplier_pangolin+': str(item.package_multiplier_pangolin_plus),
                    'package_multiplier_pangolin+AI': str(item.package_multiplier_pangolin_plus_ai),
                    'support_cost_level1': str(item.support_cost_level1),
                    'support_cost_level2': str(item.support_cost_level2),
                    'support_cost_level3': str(item.support_cost_level3)
                } for item in db_pricing]
                print(f"Loaded {len(self.pricing_data)} pricing records from database")
                return

            # If no data in DB, load from CSV
            print("No pricing data found in database, loading from CSV...")
            self._load_from_csv()
        finally:
            db.close()

    def _load_from_csv(self):
        """Fallback method to load pricing data from CSV file"""
        # Try multiple possible locations for the pricing.csv file
        possible_paths = [
            # Path when run from backend directory
            Path(__file__).parent.parent.parent / "config" / "pricing.csv",
            # Path when run from project root
            Path(__file__).parent.parent.parent.parent / "config" / "pricing.csv",
            # Path for the new location (after you move it)
            Path(__file__).parent.parent.parent / "config" / "pricing.csv"
        ]

        # Try each path until we find one that exists
        csv_path = None
        for path in possible_paths:
            if os.path.exists(path):
                csv_path = path
                break

        if not csv_path:
            raise FileNotFoundError(f"Pricing CSV file not found in any of the expected locations: {possible_paths}")

        with open(csv_path, 'r') as file:
            reader = csv.DictReader(file)
            self.pricing_data = list(reader)
            print(f"Loaded {len(self.pricing_data)} pricing records from CSV at {csv_path}")

    def _get_support_cost(self, support_level: str) -> float:
        """Return the hourly support cost for the given level using pricing data defaults."""
        if not self.pricing_data:
            return 0.0
        try:
            level_num = str(support_level).split()[-1]
            key = f"support_cost_level{level_num}"
            # Use the first row as canonical support pricing source
            return float(self.pricing_data[0].get(key, 0.0))
        except Exception:
            return 0.0

    def _support_only_result(self, support_level: str) -> Dict:
        support_cost = self._get_support_cost(support_level)
        # Add small BYOVPS management fee
        byovps_fee = 0.005  # $0.005/hour for BYOVPS management
        hourly_cost = support_cost + byovps_fee
        daily_cost = hourly_cost * 24
        monthly_cost = hourly_cost * 24 * 30
        return {
            "hourly_cost": round(hourly_cost, 4),
            "daily_cost": round(daily_cost, 2),
            "monthly_cost": round(monthly_cost, 2),
            "breakdown": {
                "instance_cost": 0.0,
                "storage_cost": 0.0,
                "network_cost": 0.0,
                "support_cost": round(support_cost, 4),
                "byovps_management_fee": round(byovps_fee, 4)
            }
        }

    def calculate_pricing(self, cloud_provider: Optional[str], instance_type: Optional[str], region: Optional[str],
                         package: str, support_level: str, server_type: str = "new") -> Dict:
        """Calculate pricing based on configuration and server type."""
        print(f"Calculating pricing for: server_type={server_type}, provider={cloud_provider}, instance_type={instance_type}, region={region}, package={package}, support={support_level}")

        if server_type in ("vps", "existing"):
            # No infrastructure costs; support-only pricing
            return self._support_only_result(support_level)

        # Require provider details for 'new' server type
        if not cloud_provider or not instance_type or not region:
            print("Missing provider details for 'new' server pricing; returning zeros")
            return {
                "hourly_cost": 0.0,
                "daily_cost": 0.0,
                "monthly_cost": 0.0,
                "breakdown": {
                    "instance_cost": 0.0,
                    "storage_cost": 0.0,
                    "network_cost": 0.0,
                    "support_cost": 0.0
                }
            }

        # Find the matching pricing row
        pricing_row = None
        for row in self.pricing_data:
            if (row['cloud_provider'] == cloud_provider and
                row['instance_type'] == instance_type and
                row['region'] == region):
                pricing_row = row
                break

        if not pricing_row:
            print(f"No matching pricing data found for {cloud_provider}, {instance_type}, {region}")
            return {
                "hourly_cost": 0.0,
                "daily_cost": 0.0,
                "monthly_cost": 0.0,
                "breakdown": {
                    "instance_cost": 0.0,
                    "storage_cost": 0.0,
                    "network_cost": 0.0,
                    "support_cost": 0.0
                }
            }

        print(f"Found pricing row: {pricing_row}")

        # Get base hourly cost
        base_hourly_cost = float(pricing_row['hourly_cost'])

        # Apply package multiplier - map package names to CSV column names
        package_name_mapping = {
            "pangolin": "package_multiplier_pangolin",
            "pangolin+": "package_multiplier_pangolin+",
            "pangolin+ai": "package_multiplier_pangolin+AI"
        }

        package_multiplier_key = package_name_mapping.get(package.lower(), "package_multiplier_pangolin")
        package_multiplier_value = pricing_row.get(package_multiplier_key, 1.0)
        package_multiplier = float(package_multiplier_value) if package_multiplier_value is not None else 1.0

        # Get support cost
        support_level_num = str(support_level).split()[-1]  # Extract level number
        support_cost_key = f"support_cost_level{support_level_num}"
        support_cost_value = pricing_row.get(support_cost_key, 0.0)
        # Handle None values that might occur from CSV parsing issues
        support_cost = float(support_cost_value) if support_cost_value is not None else 0.0

        # Calculate costs
        instance_cost = base_hourly_cost * package_multiplier
        hourly_cost = instance_cost + support_cost
        monthly_cost = hourly_cost * 24 * 30

        daily_cost = hourly_cost * 24

        result = {
            "hourly_cost": round(hourly_cost, 4),
            "daily_cost": round(daily_cost, 2),
            "monthly_cost": round(monthly_cost, 2),
            "breakdown": {
                "instance_cost": round(instance_cost, 4),
                "storage_cost": 0.0,
                "network_cost": 0.0,
                "support_cost": round(support_cost, 4)
            }
        }

        print(f"Calculated pricing: {result}")
        return result

# Create singleton instance
pricing_service = PricingService()
