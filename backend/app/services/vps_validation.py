import asyncio
import socket
from typing import Tuple

PERIPHERY_PORT = 8120  # periphery agent port
PERIPHERY_HEALTH_PATH = "/health"  # if available in periphery
PERIPHERY_MIN_VERSION = "0.1.0"


class VPSValidationService:
    async def validate_periphery_client(self, ip_address: str) -> Tuple[bool, str]:
        """
        Attempt to connect to the periphery client on the VPS to validate connectivity.
        Strategy:
        - Try TCP connect to PERIPHERY_PORT with short timeout
        - Optionally try HTTP GET to /health to check version
        """
        # First: TCP connectivity check
        tcp_ok, tcp_msg = await self._tcp_connect(ip_address, PERIPHERY_PORT, timeout=5)
        if not tcp_ok:
            return False, f"Cannot connect to periphery on {ip_address}:{PERIPHERY_PORT} - {tcp_msg}"

        # If we want a stronger check, try a simple HTTP GET to health endpoint
        try:
            import http.client
            conn = http.client.HTTPConnection(ip_address, PERIPHERY_PORT, timeout=5)
            conn.request("GET", PERIPHERY_HEALTH_PATH)
            resp = conn.getresponse()
            if resp.status == 200:
                return True, "Periphery client reachable and healthy"
            else:
                return True, f"Periphery TCP reachable; health endpoint returned {resp.status}"
        except Exception:
            # Health endpoint may not exist; TCP reachability is still OK
            return True, "Periphery client reachable"

    async def _tcp_connect(self, host: str, port: int, timeout: int = 5) -> Tuple[bool, str]:
        loop = asyncio.get_running_loop()
        try:
            fut = loop.getaddrinfo(host, port, type=socket.SOCK_STREAM)
            infos = await asyncio.wait_for(fut, timeout=timeout)
            for family, socktype, proto, canonname, sockaddr in infos:
                try:
                    reader, writer = await asyncio.wait_for(asyncio.open_connection(host=sockaddr[0], port=sockaddr[1]), timeout=timeout)
                    writer.close()
                    await writer.wait_closed()
                    return True, "connected"
                except Exception as e:
                    last_err = str(e)
                    continue
            return False, last_err if 'last_err' in locals() else 'unknown error'
        except Exception as e:
            return False, str(e)

    def generate_periphery_script(self) -> str:
        """Return a script users can run on their VPS to install and start periphery."""
        return (
            "curl -sSL https://raw.githubusercontent.com/moghtech/komodo/main/scripts/setup-periphery.py | sudo python3\n"
            "sudo systemctl enable periphery\n"
            "sudo systemctl start periphery\n"
        )

