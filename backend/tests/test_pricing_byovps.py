from app.core.pricing.pricing_service import PricingService


def test_support_only_pricing_for_vps():
    service = PricingService()
    result = service.calculate_pricing(
        cloud_provider=None,
        instance_type=None,
        region=None,
        package='Pangolin',
        support_level='Level 2',
        server_type='vps'
    )
    assert result['breakdown']['instance_cost'] == 0.0
    assert result['breakdown']['support_cost'] > 0.0
    assert result['hourly_cost'] == result['breakdown']['support_cost']


def test_support_only_pricing_for_existing():
    service = PricingService()
    result = service.calculate_pricing(
        cloud_provider=None,
        instance_type=None,
        region=None,
        package='Premium',
        support_level='Level 3',
        server_type='existing'
    )
    assert result['breakdown']['instance_cost'] == 0.0
    assert result['breakdown']['support_cost'] > 0.0
    assert result['hourly_cost'] == result['breakdown']['support_cost']

