# Deployment Creation Mode
# Set to 'false' to enable REAL deployment creation (⚠️ WILL INCUR COSTS!)
# Set to 'true' or leave unset to mock deployment creation (safe for development)
VITE_MOCK_DEPLOYMENT_CREATION=true

# Deployment System Variables
VITE_POSTGRES_HOST=pangolin-postgres
VITE_POSTGRES_USER=REPLACE_USER
VITE_POSTGRES_PASSWORD=REPLACE_PASSWORD
VITE_GITHUB_REPO=oidebrett/manidae

VITE_API_SECRET_KEY=REPLACE_API_SECRET_KEY

# Feature flags
# When set to 'true', shows BYOVPS and Existing Server options in the UI
VITE_ALLOW_VPS_REUSE=false