import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts';
import { User, UserCreate } from '../api/types';
import { apiClient } from '../api';
import { Button } from '../components/ui/button';
import { Card } from '../components/ui/card';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import Modal from '../components/ui/modal';
import { Table } from '../components/ui/table';
import { Switch } from '../components/ui/switch';
import { toast } from '../components/ui/toast';

const UserManagement: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const [newUser, setNewUser] = useState<UserCreate & { is_admin: boolean }>({
    username: '',
    email: '',
    password: '',
    is_admin: false
  });
  const [showAdjustModal, setShowAdjustModal] = useState(false);
  const [userToAdjust, setUserToAdjust] = useState<User | null>(null);
  const [adjustAmount, setAdjustAmount] = useState<string>('10');
  const [adjustDescription, setAdjustDescription] = useState<string>('');


  // Redirect if not admin
  useEffect(() => {
    if (!isAuthenticated || !user?.is_admin) {
      window.location.href = '/dashboard';
    }
  }, [isAuthenticated, user]);

  const fetchUsers = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await apiClient.getUsers();
      setUsers(response);
    } catch (err) {
      setError('Failed to load users');
      console.error('Error loading users:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isAuthenticated && user?.is_admin) {
      fetchUsers();
    }
  }, [isAuthenticated, user]);

  const handleCreateUser = async () => {
    try {
      setError(null);
      await apiClient.createUserAsAdmin({
        username: newUser.username,
        email: newUser.email,
        password: newUser.password,
        is_admin: newUser.is_admin
      });
      setShowCreateModal(false);
      setNewUser({ username: '', email: '', password: '', is_admin: false });
      await fetchUsers();
    } catch (err) {
      setError('Failed to create user');
      console.error('Error creating user:', err);
    }
  };

  const handleDeleteUser = async () => {
    if (!userToDelete) return;

    try {
      setError(null);
      await apiClient.deleteUser(userToDelete.id);
      setShowDeleteModal(false);
      setUserToDelete(null);
      await fetchUsers();
    } catch (err) {
      setError('Failed to delete user');
      console.error('Error deleting user:', err);
    }
  };

  const handleToggleAdmin = async (userId: number, currentStatus: boolean) => {
    try {
      setError(null);
      if (currentStatus) {
        await apiClient.removeAdminRole(userId);
      } else {
        await apiClient.makeUserAdmin(userId);
      }
      await fetchUsers();
    } catch (err) {
      setError('Failed to update admin status');
      console.error('Error updating admin status:', err);
    }
  };

  if (!isAuthenticated || !user?.is_admin) {
    return null; // Will redirect
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">User Management</h1>
        <Button onClick={() => setShowCreateModal(true)}>
          Create New User
        </Button>
      </div>

      {error && (
        <Card className="p-4 border-red-200 bg-red-50">
          <p className="text-red-700">{error}</p>
        </Card>
      )}

      {loading ? (
        <Card className="p-8">
          <p className="text-center">Loading users...</p>
        </Card>
      ) : (
        <Card className="p-6">
          <Table>
            <thead>
              <tr>
                <th>ID</th>
                <th>Username</th>
                <th>Email</th>
                <th>Admin</th>
                <th>Balance</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {users.map((u) => (
                <tr key={u.id}>
                  <td>{u.id}</td>
                  <td>{u.username}</td>
                  <td>{u.email}</td>
                  <td>
                    <Switch
                      checked={u.is_admin}
                      onCheckedChange={() => handleToggleAdmin(u.id, u.is_admin)}
                      disabled={u.id === user.id}
                    />
                  </td>
                  <td>{(u as any).balance != null ? parseFloat((u as any).balance).toFixed(2) : '—'}</td>
                  <td className="space-x-2">
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => {
                        setUserToAdjust(u);
                        setAdjustAmount('10');
                        setAdjustDescription('');
                        setShowAdjustModal(true);
                      }}
                    >
                      Adjust Balance
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => {
                        setUserToDelete(u);
                        setShowDeleteModal(true);
                      }}
                      disabled={u.id === user.id}
                    >
                      Delete
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </Table>
        </Card>
      )}

      {/* Create User Modal */}
      <Modal
        open={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        title="Create New User"
      >
        <div className="space-y-4">
          <div>
            <Label htmlFor="username">Username</Label>
            <Input
              id="username"
              value={newUser.username}
              onChange={(e) => setNewUser({ ...newUser, username: e.target.value })}
              placeholder="Enter username"
            />
          </div>
          <div>
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={newUser.email}
              onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
              placeholder="Enter email"
            />
          </div>
          <div>
            <Label htmlFor="password">Password</Label>
            <Input
              id="password"
              type="password"
              value={newUser.password}
              onChange={(e) => setNewUser({ ...newUser, password: e.target.value })}
              placeholder="Enter password"
            />
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="is_admin"
              checked={newUser.is_admin}
              onCheckedChange={(checked) => setNewUser({ ...newUser, is_admin: checked })}
            />
            <Label htmlFor="is_admin">Administrator</Label>
          </div>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setShowCreateModal(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleCreateUser}
              disabled={!newUser.username || !newUser.email || !newUser.password}
            >
              Create User
            </Button>
          </div>
        </div>
      </Modal>

      {/* Adjust Balance Modal */}
      <Modal
        open={showAdjustModal}
        onClose={() => setShowAdjustModal(false)}
        title={`Adjust Balance for ${userToAdjust?.username || ''}`}
      >
        <div className="space-y-4">
          <div>
            <Label htmlFor="amount">Amount (EUR)</Label>
            <Input
              id="amount"
              type="number"
              value={adjustAmount}
              onChange={(e) => setAdjustAmount(e.target.value)}
              placeholder="e.g. 10"
            />
            <p className="text-xs text-muted-foreground mt-1">Use negative numbers to subtract funds.</p>
          </div>
          <div>
            <Label htmlFor="description">Description</Label>
            <Input
              id="description"
              value={adjustDescription}
              onChange={(e) => setAdjustDescription(e.target.value)}
              placeholder="e.g. courtesy credit"
            />
          </div>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setShowAdjustModal(false)}>
              Cancel
            </Button>
            <Button
              onClick={async () => {
                if (!userToAdjust) return;
                try {
                  await apiClient.adjustUserBalance(
                    userToAdjust.id,
                    parseFloat(adjustAmount || '0'),
                    adjustDescription || undefined
                  );
                  toast('Balance adjusted successfully', 'success');
                  setShowAdjustModal(false);
                  setUserToAdjust(null);
                  await fetchUsers();
                } catch (err) {
                  console.error('Failed to adjust balance', err);
                  toast('Failed to adjust balance', 'error');
                }
              }}
              disabled={!adjustAmount || isNaN(parseFloat(adjustAmount))}
            >
              Save
            </Button>
          </div>
        </div>
      </Modal>

      {/* Delete User Modal */}
      <Modal
        open={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="Delete User"
      >
        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            Are you sure you want to delete user "{userToDelete?.username}"?
          </p>
          <p className="text-sm text-red-600 font-medium">
            Warning: This will also delete all their deployments and cannot be undone.
          </p>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setShowDeleteModal(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteUser}
            >
              Delete User
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default UserManagement;